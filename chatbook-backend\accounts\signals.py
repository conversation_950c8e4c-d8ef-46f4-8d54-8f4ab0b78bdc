from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from django.db import transaction
from employees.models import Employee
from .models.role import Role
from .models.user_settings import UserSettings
import logging

logger = logging.getLogger(__name__)

# Split into two focused handlers with unique dispatch_uid values
@receiver(post_save, sender=settings.AUTH_USER_MODEL, dispatch_uid="ensure_user_settings_and_role")
def ensure_user_settings_and_role(sender, instance, created, **kwargs):
    """
    Ensure new users have settings and appropriate role based on signup type:
    1. Create user settings for new users if not exists
    2. Assign role based on signup_type if specified, default to customer if not
    
    This handler is idempotent and can be safely executed multiple times.
    """
    if not created:
        return  # Only run for newly created users
        
    logger.info(f"Initializing settings and role for new user: {instance.email}")
    
    with transaction.atomic():
        # Create UserSettings for new user (idempotent operation)
        UserSettings.objects.get_or_create(user=instance)
        
        # Only assign role if user has no roles yet
        if not instance.roles.exists():
            # Get signup type from instance context if available
            signup_type = getattr(instance, 'signup_type', 'customer')
            
            if signup_type == 'business':
                # For business signup, assign business_admin role
                business_role, _ = Role.objects.get_or_create(name='admin')
                instance.roles.add(business_role)
                logger.info(f"Assigned business admin role to user: {instance.email}")
            else:
                # Default to customer role for regular signup
                customer_role, _ = Role.objects.get_or_create(name='customer')
                instance.roles.add(customer_role)
                logger.info(f"Assigned customer role to user: {instance.email}")

@receiver(post_save, sender=settings.AUTH_USER_MODEL, dispatch_uid="sync_employee_profile")
def sync_employee_profile(sender, instance, **kwargs):
    """
    Synchronize employee profiles with user data:
    - Create or update the Employee profile when user has the employee role
    
    This handler is idempotent and can be safely executed multiple times.
    """
    # Skip if user doesn't have employee role to avoid unnecessary processing
    if not instance.roles.filter(name='employee').exists():
        return
        
    logger.info(f"Syncing employee profile for user: {instance.email}")
    
    with transaction.atomic():
        try:
            # Try to find existing employee profile linked to this user
            employee = Employee.objects.get(user=instance)
            # Employee exists and is already linked - nothing to do
            logger.info(f"Employee profile already exists for {instance.email}")
                
        except Employee.DoesNotExist:
            # Create new employee profile if doesn't exist
            logger.info(f"Creating Employee profile for {instance.email}")
            Employee.objects.create(
                user=instance,
                is_active=True
            ) 