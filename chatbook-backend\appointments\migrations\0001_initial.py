# Generated by Django 4.2.22 on 2025-08-07 12:43

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('start_time', models.DateTimeField(help_text='Start time of the appointment')),
                ('status', models.CharField(choices=[('requested', 'Requested'), ('confirmed', 'Confirmed'), ('accepted', 'Accepted'), ('checked_in', 'Checked In'), ('service_started', 'Service Started'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show')], default='requested', max_length=20)),
                ('payment_status', models.CharField(choices=[('unpaid', 'Unpaid'), ('paid', 'Paid'), ('refunded', 'Refunded')], default='unpaid', max_length=20)),
                ('source', models.CharField(choices=[('admin', 'Admin Interface'), ('online', 'Online Booking')], default='admin', help_text='Source of the appointment booking', max_length=20)),
                ('notes_from_customer', models.TextField(blank=True, help_text='Any special notes from the customer for this appointment')),
                ('cancellation_reason', models.TextField(blank=True, help_text='Reason for cancellation if appointment was cancelled')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Appointment',
                'verbose_name_plural': 'Appointments',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='AppointmentAddOn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_on_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('duration', models.PositiveIntegerField(default=0, help_text='Duration in minutes for this add-on')),
            ],
        ),
        migrations.CreateModel(
            name='AppointmentHistory',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('status_changed', 'Status Changed'), ('payment_updated', 'Payment Updated'), ('service_added', 'Service Added'), ('service_removed', 'Service Removed'), ('service_modified', 'Service Modified'), ('addon_added', 'Add-on Added'), ('addon_removed', 'Add-on Removed'), ('addon_modified', 'Add-on Modified'), ('rescheduled', 'Rescheduled'), ('cancelled', 'Cancelled'), ('deleted', 'Deleted')], help_text='Type of action performed', max_length=20)),
                ('modified_by', models.CharField(default='System', help_text='Name of the user who made this change', max_length=255)),
                ('field_name', models.CharField(blank=True, help_text='Name of the field that was changed (if applicable)', max_length=100)),
                ('old_value', models.JSONField(blank=True, help_text='Previous value before the change', null=True)),
                ('new_value', models.JSONField(blank=True, help_text='New value after the change', null=True)),
                ('change_summary', models.TextField(help_text='Human-readable description of what changed')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the user who made the change', null=True)),
                ('user_agent', models.TextField(blank=True, default='', help_text='User agent string of the client that made the change')),
                ('modified_at', models.DateTimeField(auto_now_add=True, help_text='When this change was made')),
            ],
            options={
                'verbose_name': 'Appointment History',
                'verbose_name_plural': 'Appointment History',
                'ordering': ['-modified_at'],
            },
        ),
        migrations.CreateModel(
            name='RecurringPattern',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('frequency', models.CharField(choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly')], default='WEEKLY', max_length=10)),
                ('interval', models.IntegerField(default=1)),
                ('by_day', models.CharField(blank=True, default='--', max_length=100)),
                ('start_date', models.DateField(auto_now_add=True)),
                ('end_date', models.DateField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='AppointmentService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Number of times this service is booked', validators=[django.core.validators.MinValueValidator(1)])),
                ('base_price', models.DecimalField(decimal_places=2, help_text='Original price of the service', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('price_override', models.DecimalField(blank=True, decimal_places=2, help_text='Override price for this service (if different from base price)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('duration', models.PositiveIntegerField(help_text='Duration in minutes for this service')),
                ('buffer_time', models.PositiveIntegerField(default=0, help_text='Buffer time in minutes after this service')),
                ('notes', models.TextField(blank=True, help_text='Any special notes for this service')),
                ('appointment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointment_services', to='appointments.appointment')),
            ],
            options={
                'verbose_name': 'Appointment Service',
                'verbose_name_plural': 'Appointment Services',
                'ordering': ['appointment', 'service'],
            },
        ),
    ]
