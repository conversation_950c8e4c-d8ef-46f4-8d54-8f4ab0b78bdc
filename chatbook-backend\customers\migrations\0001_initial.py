# Generated by Django 4.2.22 on 2025-08-07 12:43

import customers.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('business', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_on_file', models.BooleanField(default=False)),
                ('birthdate', models.CharField(blank=True, help_text="Customer's birthdate", max_length=100, null=True)),
                ('gender', models.CharField(blank=True, help_text="Customer's gender", max_length=50, null=True)),
                ('address_street', models.Char<PERSON>ield(blank=True, help_text='Street address', max_length=255, null=True)),
                ('address_apt_suite', models.Char<PERSON>ield(blank=True, help_text='Apartment/Suite number', max_length=100, null=True)),
                ('address_city', models.CharField(blank=True, help_text='City', max_length=100, null=True)),
                ('address_state', models.CharField(blank=True, help_text='State/Province', max_length=100, null=True)),
                ('address_zip', models.CharField(blank=True, help_text='ZIP/Postal code', max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='customer_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Customer Profile',
                'verbose_name_plural': 'Customer Profiles',
            },
        ),
        migrations.CreateModel(
            name='CustomerTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('color', models.CharField(default='#000000', max_length=7)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Customer Tag',
                'verbose_name_plural': 'Customer Tags',
            },
        ),
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_id', models.CharField(max_length=50, unique=True)),
                ('file', models.FileField(upload_to=customers.models.upload_file_path)),
                ('file_name', models.CharField(max_length=255)),
                ('file_size', models.IntegerField()),
                ('file_type', models.CharField(default='customer_import', max_length=50)),
                ('description', models.TextField(blank=True)),
                ('skip_duplicates', models.BooleanField(default=True)),
                ('update_existing', models.BooleanField(default=False)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('queued', 'Queued'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='uploaded', max_length=20)),
                ('total_rows', models.IntegerField(blank=True, null=True)),
                ('processed_rows', models.IntegerField(default=0)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Uploaded File',
                'verbose_name_plural': 'Uploaded Files',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='SignedForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('form_type', models.CharField(max_length=100)),
                ('content', models.TextField()),
                ('signature', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('signed_at', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signed_forms', to='customers.customerprofile')),
            ],
            options={
                'verbose_name': 'Signed Form',
                'verbose_name_plural': 'Signed Forms',
            },
        ),
        migrations.CreateModel(
            name='CustomerFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('file', models.FileField(upload_to='customer_files/')),
                ('description', models.TextField(blank=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='customers.customerprofile')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Customer File',
                'verbose_name_plural': 'Customer Files',
            },
        ),
        migrations.CreateModel(
            name='CustomerBookingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(blank=True, db_index=True, help_text='Django session key for anonymous sessions', max_length=40, null=True)),
                ('booking_data', models.JSONField(default=dict, help_text='Complete booking data including service, employee, date, time, customer info, etc.')),
                ('current_step', models.CharField(choices=[('service-selection', 'Service Selection'), ('time-selection', 'Time Selection'), ('consent', 'Consent'), ('review', 'Review'), ('confirmation', 'Confirmation')], default='service-selection', max_length=20)),
                ('consent_status', models.CharField(choices=[('not_signed', 'Not Signed'), ('signed', 'Signed'), ('expired', 'Expired')], default='not_signed', help_text='Status of consent form signature', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField(help_text='When this booking session expires')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_booking_sessions', to='business.business')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='booking_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Customer Booking Session',
                'verbose_name_plural': 'Customer Booking Sessions',
                'ordering': ['-updated_at'],
                'indexes': [models.Index(fields=['user', 'business'], name='customers_c_user_id_906a27_idx'), models.Index(fields=['session_key'], name='customers_c_session_ddb51e_idx'), models.Index(fields=['expires_at'], name='customers_c_expires_0634d4_idx'), models.Index(fields=['user', 'business', 'current_step'], name='customers_c_user_id_b34b1b_idx')],
                'unique_together': {('user', 'business')},
            },
        ),
    ]
