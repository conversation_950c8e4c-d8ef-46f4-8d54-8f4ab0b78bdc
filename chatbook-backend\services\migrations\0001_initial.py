# Generated by Django 4.2.22 on 2025-08-07 12:43

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('business', '0001_initial'),
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AddOn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('short_name', models.CharField(blank=True, help_text='Short display name for the add-on', max_length=50)),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('base_duration', models.DurationField(help_text='Duration of the add-on')),
                ('description', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='addons/')),
                ('display_order', models.IntegerField(default=0, help_text='Order in which add-on is displayed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='add_ons', to='business.business')),
            ],
            options={
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('is_active', models.BooleanField(default=True)),
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('short_name', models.CharField(blank=True, help_text='Short display name for the service', max_length=100)),
                ('description', models.TextField(blank=True)),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('base_duration', models.DurationField(help_text='Duration of the service')),
                ('buffer_time', models.DurationField(default=datetime.timedelta(seconds=900), help_text='Buffer time before and after the service')),
                ('show_online', models.BooleanField(default=True, help_text='Show this service in online booking system')),
                ('display_order', models.IntegerField(default=0, help_text='Order in which service is displayed')),
                ('image', models.ImageField(blank=True, null=True, upload_to='services/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='services', to='business.business')),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['category', 'display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='StyleGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('display_order', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Style Group',
                'verbose_name_plural': 'Style Groups',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='StylistLevelAddOn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('duration', models.DurationField(help_text='Duration of the add-on for this stylist level')),
                ('is_offered', models.BooleanField(default=True, help_text='Whether this stylist level can offer this add-on')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('addon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stylist_level_addons', to='services.addon')),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stylist_level_addon_pricing', to='business.business')),
                ('stylist_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addon_pricing', to='business.stylistlevel', verbose_name='Stylist Level')),
            ],
            options={
                'verbose_name': 'Stylist Level Add-On',
                'verbose_name_plural': 'Stylist Level Add-Ons',
                'ordering': ['addon', 'stylist_level'],
            },
        ),
        migrations.CreateModel(
            name='ServiceCategory',
            fields=[
                ('is_active', models.BooleanField(default=True)),
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('order', models.IntegerField(default=0)),
                ('color', models.CharField(blank=True, choices=[('', 'No Color'), ('#6D6E6F', 'Dark Gray'), ('#FFFFFF', 'White'), ('#E91E63', 'Default Pink'), ('#E6499A', 'Pink'), ('#F27679', 'Light Red'), ('#EF484A', 'Red'), ('#F69681', 'Coral'), ('#F26822', 'Orange'), ('#FAA53E', 'Light Orange'), ('#FAF6A7', 'Light Yellow'), ('#FDD628', 'Yellow'), ('#CBDD4E', 'Light Green'), ('#7DC242', 'Green'), ('#11A44A', 'Dark Green'), ('#68C4A0', 'Mint'), ('#0EB9A4', 'Teal'), ('#478CCA', 'Blue'), ('#7AA6BA', 'Light Blue'), ('#C3BFDF', 'Light Purple'), ('#EFBBD6', 'Light Pink'), ('#A391C5', 'Purple'), ('#B668AA', 'Dark Purple'), ('#B4B5B4', 'Gray'), ('#FFF8E9', 'Cream'), ('#E5B67F', 'Tan'), ('#B47E62', 'Brown'), ('#D6D6D6', 'Light Gray')], default='#E91E63', help_text='Select a color for category display', max_length=7)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_categories', to='business.business')),
            ],
            options={
                'verbose_name': 'Service Category',
                'verbose_name_plural': 'Service Categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ServiceAddOn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('is_required', models.BooleanField(default=False, help_text='Whether this add-on is required for the service')),
                ('price_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('duration_override', models.DurationField(blank=True, null=True)),
                ('display_order', models.IntegerField(default=0, help_text='Order in which add-ons are displayed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('addon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_addons', to='services.addon')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_addons', to='services.service')),
            ],
            options={
                'verbose_name': 'Service add-on',
                'verbose_name_plural': 'Service add-ons',
                'ordering': ['display_order', 'addon__name'],
            },
        ),
        migrations.AddField(
            model_name='service',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services', to='services.servicecategory'),
        ),
        migrations.AddField(
            model_name='service',
            name='style_group',
            field=models.ForeignKey(blank=True, help_text='Style group for service progression (e.g., Classic, Styling, Volume, Real Mink)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services', to='services.stylegroup'),
        ),
        migrations.CreateModel(
            name='LashServiceSuggestionRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('last_service_style_group_name', models.CharField(help_text='Denormalized style group name for display and filtering (e.g., Classic, Styling, Volume, Real Mink)', max_length=50)),
                ('min_days_since', models.IntegerField(help_text='Minimum days since last service (inclusive)')),
                ('max_days_since', models.IntegerField(help_text='Maximum days since last service (exclusive)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lash_service_suggestion_rules', to='business.business')),
                ('last_service_style_group', models.ForeignKey(help_text='Style group ID of the last service for efficient querying', on_delete=django.db.models.deletion.CASCADE, related_name='triggers_service_suggestions', to='services.stylegroup')),
                ('next_service', models.ForeignKey(help_text='The service to suggest next', on_delete=django.db.models.deletion.CASCADE, related_name='suggested_as_next_service', to='services.service')),
            ],
            options={
                'verbose_name': 'Lash Service Suggestion Rule',
                'verbose_name_plural': 'Lash Service Suggestion Rules',
                'ordering': ['business', 'last_service_style_group__name', 'min_days_since'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('custom_price', models.DecimalField(decimal_places=2, default=0.0, help_text='Use 0.00 to use service.base_price', max_digits=10)),
                ('custom_duration', models.DurationField(blank=True, null=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_employee_services', to='business.business')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_services', to='employees.employee')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_services', to='services.service')),
            ],
        ),
        migrations.CreateModel(
            name='EmployeeAddOn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('custom_price', models.DecimalField(decimal_places=2, default=0.0, help_text='Use 0.00 to use addon.base_price', max_digits=10)),
                ('custom_duration', models.DurationField(blank=True, null=True)),
                ('addon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_addons', to='services.addon')),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_employee_addons', to='business.business')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_addons', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Employee Add-On',
                'verbose_name_plural': 'Employee Add-Ons',
            },
        ),
        migrations.CreateModel(
            name='AddonSuggestionRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('new_client_only', models.BooleanField(default=False, help_text='Only suggest this add-on to new clients')),
                ('min_days_since', models.IntegerField(blank=True, help_text='Minimum days since last add-on (inclusive)', null=True)),
                ('max_days_since', models.IntegerField(blank=True, help_text='Maximum days since last add-on (exclusive)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addon_suggestion_rules', to='business.business')),
                ('last_addon', models.ForeignKey(blank=True, help_text='Suggest the add-on if customer previously had this add-on', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='triggers_suggestions', to='services.addon')),
                ('suggested_addon', models.ForeignKey(help_text='The add-on to suggest', on_delete=django.db.models.deletion.CASCADE, related_name='suggestion_rules', to='services.addon')),
            ],
            options={
                'verbose_name': 'Add-on Suggestion Rule',
                'verbose_name_plural': 'Add-on Suggestion Rules',
                'ordering': ['business', 'suggested_addon__name'],
            },
        ),
        migrations.AddField(
            model_name='addon',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='addons', to='services.servicecategory'),
        ),
        migrations.CreateModel(
            name='StylistLevelService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('duration', models.DurationField(help_text='Duration of the service for this stylist level')),
                ('is_offered', models.BooleanField(default=True, help_text='Whether this stylist level can offer this service')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stylist_level_pricing', to='business.business')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stylist_level_services', to='services.service')),
                ('stylist_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_pricing', to='business.stylistlevel', verbose_name='Stylist Level')),
            ],
            options={
                'verbose_name': 'Stylist Level Service',
                'verbose_name_plural': 'Stylist Level Services',
                'ordering': ['service', 'stylist_level'],
                'indexes': [models.Index(condition=models.Q(('is_active', True)), fields=['business', 'service', 'stylist_level'], name='idx_level_pricing'), models.Index(condition=models.Q(('is_active', True), ('is_offered', True)), fields=['business', 'service', 'stylist_level', 'is_offered'], name='idx_level_service_offering')],
            },
        ),
        migrations.AddConstraint(
            model_name='stylistlevelservice',
            constraint=models.UniqueConstraint(fields=('business', 'service', 'stylist_level'), name='uniq_pricing_per_level_service'),
        ),
        migrations.AddConstraint(
            model_name='stylistlevelservice',
            constraint=models.CheckConstraint(check=models.Q(('price__gte', 0)), name='positive_level_price'),
        ),
        migrations.AddConstraint(
            model_name='stylistlevelservice',
            constraint=models.CheckConstraint(check=models.Q(('duration__gt', datetime.timedelta(0))), name='positive_level_duration'),
        ),
        migrations.AddIndex(
            model_name='stylistleveladdon',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'addon', 'stylist_level'], name='idx_level_addon_pricing'),
        ),
        migrations.AddIndex(
            model_name='stylistleveladdon',
            index=models.Index(condition=models.Q(('is_active', True), ('is_offered', True)), fields=['business', 'addon', 'stylist_level', 'is_offered'], name='idx_level_addon_offering'),
        ),
        migrations.AddConstraint(
            model_name='stylistleveladdon',
            constraint=models.UniqueConstraint(fields=('business', 'addon', 'stylist_level'), name='uniq_pricing_per_level_addon'),
        ),
        migrations.AddConstraint(
            model_name='stylistleveladdon',
            constraint=models.CheckConstraint(check=models.Q(('price__gte', 0)), name='positive_level_addon_price'),
        ),
        migrations.AddConstraint(
            model_name='stylistleveladdon',
            constraint=models.CheckConstraint(check=models.Q(('duration__gt', datetime.timedelta(0))), name='positive_level_addon_duration'),
        ),
        migrations.AddIndex(
            model_name='servicecategory',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business'], name='idx_category_business'),
        ),
        migrations.AddConstraint(
            model_name='servicecategory',
            constraint=models.UniqueConstraint(fields=('business', 'name'), name='uniq_category_per_business'),
        ),
        migrations.AddIndex(
            model_name='serviceaddon',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['service'], name='idx_serviceaddon_service'),
        ),
        migrations.AddConstraint(
            model_name='serviceaddon',
            constraint=models.UniqueConstraint(fields=('service', 'addon'), name='uniq_addon_per_service'),
        ),
        migrations.AddConstraint(
            model_name='serviceaddon',
            constraint=models.CheckConstraint(check=models.Q(('price_override__gte', 0), ('price_override__isnull', True), _connector='OR'), name='positive_price_override'),
        ),
        migrations.AddConstraint(
            model_name='serviceaddon',
            constraint=models.CheckConstraint(check=models.Q(('duration_override__gt', datetime.timedelta(0)), ('duration_override__isnull', True), _connector='OR'), name='positive_duration_override'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'category'], name='svc_active_biz_cat_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(condition=models.Q(('is_active', True), ('show_online', True)), fields=['business', 'show_online'], name='idx_service_online_booking'),
        ),
        migrations.AddConstraint(
            model_name='service',
            constraint=models.CheckConstraint(check=models.Q(('base_duration__gt', datetime.timedelta(0)), ('buffer_time__gte', datetime.timedelta(0))), name='positive_durations'),
        ),
        migrations.AddConstraint(
            model_name='service',
            constraint=models.CheckConstraint(check=models.Q(('base_price__gte', 0)), name='positive_service_price'),
        ),
        migrations.AddIndex(
            model_name='lashservicesuggestionrule',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'last_service_style_group'], name='idx_service_sugg_biz_style'),
        ),
        migrations.AddIndex(
            model_name='lashservicesuggestionrule',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'min_days_since', 'max_days_since'], name='idx_service_sugg_timing'),
        ),
        migrations.AddConstraint(
            model_name='lashservicesuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('min_days_since__gte', 0)), name='positive_min_days_since_service'),
        ),
        migrations.AddConstraint(
            model_name='lashservicesuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('max_days_since__gt', 0)), name='positive_max_days_since_service'),
        ),
        migrations.AddConstraint(
            model_name='lashservicesuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('min_days_since__lt', models.F('max_days_since'))), name='min_less_than_max_days_service'),
        ),
        migrations.AddConstraint(
            model_name='lashservicesuggestionrule',
            constraint=models.UniqueConstraint(fields=('business', 'last_service_style_group', 'next_service', 'min_days_since'), name='unique_style_group_suggestion_rule'),
        ),
        migrations.AddIndex(
            model_name='employeeservice',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'employee'], name='idx_employeeservice_biz_emp'),
        ),
        migrations.AddConstraint(
            model_name='employeeservice',
            constraint=models.UniqueConstraint(fields=('business', 'employee', 'service'), name='uniq_service_per_employee_business'),
        ),
        migrations.AddConstraint(
            model_name='employeeservice',
            constraint=models.CheckConstraint(check=models.Q(('custom_price__gte', 0)), name='positive_custom_price'),
        ),
        migrations.AddConstraint(
            model_name='employeeservice',
            constraint=models.CheckConstraint(check=models.Q(('custom_duration__gt', datetime.timedelta(0)), ('custom_duration__isnull', True), _connector='OR'), name='positive_custom_duration'),
        ),
        migrations.AddIndex(
            model_name='employeeaddon',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'employee'], name='idx_employeeaddon_biz_emp'),
        ),
        migrations.AddConstraint(
            model_name='employeeaddon',
            constraint=models.UniqueConstraint(fields=('business', 'employee', 'addon'), name='uniq_addon_per_employee_business'),
        ),
        migrations.AddConstraint(
            model_name='employeeaddon',
            constraint=models.CheckConstraint(check=models.Q(('custom_price__gte', 0)), name='positive_custom_addon_price'),
        ),
        migrations.AddConstraint(
            model_name='employeeaddon',
            constraint=models.CheckConstraint(check=models.Q(('custom_duration__gt', datetime.timedelta(0)), ('custom_duration__isnull', True), _connector='OR'), name='positive_custom_addon_duration'),
        ),
        migrations.AddIndex(
            model_name='addonsuggestionrule',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business', 'suggested_addon'], name='idx_addon_sugg_biz_addon'),
        ),
        migrations.AddIndex(
            model_name='addonsuggestionrule',
            index=models.Index(condition=models.Q(('is_active', True), ('last_addon__isnull', False)), fields=['business', 'last_addon'], name='idx_addon_sugg_last_addon'),
        ),
        migrations.AddConstraint(
            model_name='addonsuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('min_days_since__gte', 0), ('min_days_since__isnull', True), _connector='OR'), name='positive_min_days_since'),
        ),
        migrations.AddConstraint(
            model_name='addonsuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('max_days_since__gt', 0), ('max_days_since__isnull', True), _connector='OR'), name='positive_max_days_since'),
        ),
        migrations.AddConstraint(
            model_name='addonsuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('min_days_since__lt', models.F('max_days_since')), ('min_days_since__isnull', True), ('max_days_since__isnull', True), _connector='OR'), name='min_less_than_max_days'),
        ),
        migrations.AddConstraint(
            model_name='addonsuggestionrule',
            constraint=models.CheckConstraint(check=models.Q(('new_client_only', True), ('last_addon__isnull', False), ('min_days_since__isnull', False), ('max_days_since__isnull', False), _connector='OR'), name='at_least_one_criteria'),
        ),
        migrations.AddIndex(
            model_name='addon',
            index=models.Index(condition=models.Q(('is_active', True)), fields=['business'], name='idx_addon_business'),
        ),
        migrations.AddConstraint(
            model_name='addon',
            constraint=models.UniqueConstraint(fields=('business', 'name'), name='uniq_addon_per_business'),
        ),
        migrations.AddConstraint(
            model_name='addon',
            constraint=models.CheckConstraint(check=models.Q(('base_price__gte', 0)), name='positive_addon_price'),
        ),
        migrations.AddConstraint(
            model_name='addon',
            constraint=models.CheckConstraint(check=models.Q(('base_duration__gt', datetime.timedelta(0))), name='positive_addon_duration'),
        ),
    ]
