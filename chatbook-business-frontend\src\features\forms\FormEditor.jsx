import { useState } from 'react'
import { useForms } from './FormsContext'

const questionTypes = [
  { id: 'short', label: 'Short Answer', icon: '📝' },
  { id: 'long', label: 'Long Answer', icon: '📄' },
  { id: 'multiple', label: 'Multiple Choice', icon: '☑️' },
  { id: 'single', label: 'Single Choice', icon: '⭕' },
  { id: 'date', label: 'Date', icon: '📅' },
  { id: 'file', label: 'File Upload', icon: '📎' },
]

function Question({ question, index, onUpdate, onDelete }) {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4">
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <input
            type="text"
            value={question.label}
            onChange={(e) => onUpdate(index, { ...question, label: e.target.value })}
            className="w-full p-2 border rounded"
            placeholder="Question label"
          />
          {question.type === 'multiple' || question.type === 'single' ? (
            <div className="mt-2">
              {question.options?.map((option, optionIndex) => (
                <div key={optionIndex} className="flex items-center gap-2 mt-1">
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => {
                      const newOptions = [...question.options]
                      newOptions[optionIndex] = e.target.value
                      onUpdate(index, { ...question, options: newOptions })
                    }}
                    className="flex-1 p-2 border rounded"
                    placeholder={`Option ${optionIndex + 1}`}
                  />
                  <button
                    onClick={() => {
                      const newOptions = question.options.filter((_, i) => i !== optionIndex)
                      onUpdate(index, { ...question, options: newOptions })
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    ×
                  </button>
                </div>
              ))}
              <button
                onClick={() => {
                  const newOptions = [...(question.options || []), '']
                  onUpdate(index, { ...question, options: newOptions })
                }}
                className="mt-2 text-blue-500 hover:text-blue-700"
              >
                + Add Option
              </button>
            </div>
          ) : null}
        </div>
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={question.required}
              onChange={(e) => onUpdate(index, { ...question, required: e.target.checked })}
              className="rounded"
            />
            Required
          </label>
          <button
            onClick={() => onDelete(index)}
            className="text-red-500 hover:text-red-700 p-2"
            title="Delete question"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  )
}

function QuestionTypeCard({ type, onClick }) {
  return (
    <button
      onClick={() => onClick(type)}
      className="w-full bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:bg-gray-50 transition-all duration-200 text-left"
    >
      <div className="flex items-center gap-3">
        <span className="text-2xl">{type.icon}</span>
        <span className="font-medium">{type.label}</span>
      </div>
    </button>
  )
}

export default function FormEditor({ formId }) {
  const { forms, updateForm } = useForms()
  const form = forms.find(f => f.id === formId)
  const [questions, setQuestions] = useState(form?.questions || [])

  const handleAddQuestion = (type) => {
    const newQuestion = {
      id: Date.now(),
      type: type.id,
      label: `New ${type.label}`,
      required: false,
      options: type.id === 'multiple' || type.id === 'single' ? [''] : undefined,
    }
    const newQuestions = [...questions, newQuestion]
    setQuestions(newQuestions)
    updateForm(formId, { questions: newQuestions })
  }

  const handleUpdateQuestion = (index, updatedQuestion) => {
    const newQuestions = [...questions]
    newQuestions[index] = updatedQuestion
    setQuestions(newQuestions)
    updateForm(formId, { questions: newQuestions })
  }

  const handleDeleteQuestion = (index) => {
    const newQuestions = questions.filter((_, i) => i !== index)
    setQuestions(newQuestions)
    updateForm(formId, { 
      questions: newQuestions,
      name: form?.name,
      documentType: form?.documentType,
      status: form?.status || 'Draft'
    })
  }

  return (
    <div className="container mx-auto p-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1">
          <h2 className="text-xl font-semibold mb-4">Question Types</h2>
          <div className="space-y-4">
            {questionTypes.map((type) => (
              <QuestionTypeCard
                key={type.id}
                type={type}
                onClick={handleAddQuestion}
              />
            ))}
          </div>
        </div>
        <div className="md:col-span-3">
          <h2 className="text-xl font-semibold mb-4">Form Questions</h2>
          <div className="space-y-4">
            {questions.map((question, index) => (
              <Question
                key={question.id}
                question={question}
                index={index}
                onUpdate={handleUpdateQuestion}
                onDelete={handleDeleteQuestion}
              />
            ))}
            {questions.length === 0 && (
              <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                Click on a question type to add it to your form
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 