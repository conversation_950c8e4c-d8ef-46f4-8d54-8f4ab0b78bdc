import { useState, useRef, useEffect } from 'react'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { useForms } from '../features/forms/FormsContext'
import Signature from '../components/Signature'
import FormPreviewFrame from '../components/FormPreviewFrame'
import SaveSuccessModal from '../components/SaveSuccessModal'
import { saveFormToPdf } from '../utils'

// Reusable textarea that automatically resizes to fit its content and
// keeps the correct height on remounts or tab switches.
function AutoResizeTextarea({ value, onChange, placeholder, className = '' }) {
  const ref = useRef(null)

  // Adjust height whenever the value changes or on initial mount
  useEffect(() => {
    if (ref.current) {
      ref.current.style.height = 'auto'
      ref.current.style.height = `${ref.current.scrollHeight}px`
    }
  }, [value])

  return (
    <textarea
      ref={ref}
      rows={1}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={`w-full border rounded px-2 py-2 text-sm resize-none overflow-hidden ${className}`}
    />
  )
}

const questionTypes = [
  { type: 'short', label: 'Short Answer', icon: (
    <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2"><line x1="4" y1="8" x2="20" y2="8" /><line x1="4" y1="16" x2="14" y2="16" /></svg>
  ) },
  { type: 'long', label: 'Long Answer', icon: (
    <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2"><line x1="4" y1="6" x2="20" y2="6" /><line x1="4" y1="12" x2="20" y2="12" /><line x1="4" y1="18" x2="14" y2="18" /></svg>
  ) },
  { type: 'multiple', label: 'Multiple Choice', icon: (
    <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2"><rect x="4" y="4" width="16" height="16" rx="2" /><polyline points="8,12 11,15 16,10" /></svg>
  ) },
  { type: 'text', label: 'Text', icon: (
    <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2"><line x1="4" y1="6" x2="20" y2="6" /><line x1="4" y1="12" x2="20" y2="12" /><line x1="4" y1="18" x2="20" y2="18" /></svg>
  ) },
  { type: 'signature', label: 'Signature', icon: (
    <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M3 17c2 0 3-3 5-3s3 3 5 3 3-3 5-3 3 3 3 3"/><path d="M9 13c.5-1.5 2.5-1.5 3 0"/><path d="M15 13c.5-1.5 2.5-1.5 3 0"/></svg>
  ) },
]

function DraggableQuestionType({ qt }) {
  const handleDragStart = (e) => {
    e.dataTransfer.setData('questionType', JSON.stringify(qt))
    e.dataTransfer.effectAllowed = 'copy'
  }
  return (
    <div
      draggable
      onDragStart={handleDragStart}
      className="flex flex-col items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-blue-50 transition-all cursor-move select-none"
      style={{ minHeight: '90px' }}
    >
      <div className="mb-2 text-2xl text-gray-700">{qt.icon}</div>
      <div className="text-sm font-medium text-gray-900 text-center">{qt.label}</div>
    </div>
  )
}

function FormsEdit() {
  const { id } = useParams()
  const location = useLocation()
  const navigate = useNavigate()
  const { forms, addForm, updateForm } = useForms()

  let formName = 'Untitled Form'
  let initialQuestions = []
  let documentType = ''
  let createdBy = 'Super Admin'
  let createdDate = new Date().toISOString().slice(0, 10)
  let initialMandatory = false
  let initialNotify = false
  let initialExpiration = ''

  if (id && id !== 'new') {
    const form = forms.find(f => f.id === parseInt(id, 10))
    if (form) {
      formName = form.name
      initialQuestions = form.questions || []
      documentType = form.documentType
      createdBy = form.createdBy
      createdDate = form.createdDate
      initialMandatory = form.mandatory || false
      initialNotify = form.notify || false
      initialExpiration = form.expiration || ''
    }
  } else if (location.state && location.state.templateId) {
    const template = forms.find(f => f.id === location.state.templateId)
    if (template) {
      formName = template.name
      initialQuestions = (template.questions || []).map((q, idx) => ({
        id: idx + 1,
        type: q.type,
        label: '',
        options: q.type === 'multiple' ? ['Option 1'] : undefined,
        required: false,
        ...(q.type === 'text' ? { textStyle: 'Heading', textContent: '' } : {}),
      }))
      documentType = template.documentType
      createdBy = template.createdBy
      createdDate = new Date().toISOString().slice(0, 10)
      initialMandatory = template.mandatory || false
      initialNotify = template.notify || false
      initialExpiration = template.expiration || ''
    }
  }

  const [questions, setQuestions] = useState(initialQuestions)
  const [name, setName] = useState(formName)
  const [mandatory, setMandatory] = useState(initialMandatory)
  const [notify, setNotify] = useState(initialNotify)
  const [expiration, setExpiration] = useState(initialExpiration)
  const [viewMode, setViewMode] = useState('design')
  const [draggedQid, setDraggedQid] = useState(null)
  const [dragOverQid, setDragOverQid] = useState(null)
  const [openMenuQid, setOpenMenuQid] = useState(null)
  const [isSaving, setIsSaving] = useState(false)
  const [savingStatus, setSavingStatus] = useState(null) // 'Draft' | 'Published' | null
  const [saveError, setSaveError] = useState(null)
  const [saveSuccess, setSaveSuccess] = useState({ visible: false, status: '' })
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false)
  const menuRef = useRef(null)

  // Update questions when forms data changes
  useEffect(() => {
    if (id && id !== 'new') {
      const form = forms.find(f => f.id === parseInt(id, 10))
      if (form && form.questions) {
        setQuestions(form.questions)
        setName(form.name)
        setMandatory(form.mandatory || false)
        setNotify(form.notify || false)
        setExpiration(form.expiration || '')
      }
    }
  }, [forms, id])

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setOpenMenuQid(null)
      }
    }
    if (openMenuQid !== null) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [openMenuQid])

  // Drag-and-drop handlers
  const handleDragOver = (e) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'copy'
  }

  const handleDrop = (e) => {
    e.preventDefault()
    const typeData = e.dataTransfer.getData('questionType')
    if (typeData) {
      const qt = JSON.parse(typeData)
      const newId = questions.length ? Math.max(...questions.map(q => q.id)) + 1 : 1
      setQuestions([
        ...questions,
        {
          id: newId,
          type: qt.type,
          label: '',
          options: qt.type === 'multiple' ? ['Option 1'] : undefined,
          required: false,
          ...(qt.type === 'text' ? { textStyle: 'Heading', textContent: '' } : {}),
          ...(qt.type === 'signature' ? { signatureData: { customer: null, agreed: false } } : {}),
        }
      ])
      return
    }
    // Handle reorder drop
    if (draggedQid !== null && dragOverQid !== null && draggedQid !== dragOverQid) {
      const fromIdx = questions.findIndex(q => q.id === draggedQid)
      const toIdx = questions.findIndex(q => q.id === dragOverQid)
      if (fromIdx !== -1 && toIdx !== -1) {
        const newQuestions = [...questions]
        const [moved] = newQuestions.splice(fromIdx, 1)
        newQuestions.splice(toIdx, 0, moved)
        setQuestions(newQuestions)
      }
    }
    setDraggedQid(null)
    setDragOverQid(null)
  }

  const updateQuestion = (qid, field, value) => {
    setQuestions(questions.map(q => q.id === qid ? { ...q, [field]: value } : q))
  }

  const updateOption = (qid, idx, value) => {
    setQuestions(questions.map(q =>
      q.id === qid ? { ...q, options: q.options.map((opt, i) => i === idx ? value : opt) } : q
    ))
  }

  const addOption = (qid) => {
    setQuestions(questions.map(q =>
      q.id === qid ? { ...q, options: [...q.options, `Option ${q.options.length + 1}`] } : q
    ))
  }

  const removeOption = (qid, idx) => {
    setQuestions(questions.map(q =>
      q.id === qid ? { ...q, options: q.options.filter((_, i) => i !== idx) } : q
    ))
  }

  const removeQuestion = (qid) => {
    try {
      console.log('Attempting to remove question with ID:', qid)
      console.log('Current questions:', questions)

      // Find the question being removed to check if it's a signature question
      const questionToRemove = questions.find(q => q.id === qid)

      if (!questionToRemove) {
        console.error('Question not found with ID:', qid)
        return
      }

      console.log('Question to remove:', questionToRemove)

      // Filter out the question from the array
      const newQuestions = questions.filter(q => q.id !== qid)

      console.log('New questions array:', newQuestions)

      // If it's a signature question, we might want to clean up any associated data
      if (questionToRemove.type === 'signature') {
        console.log('Removing signature question:', questionToRemove)
        // Clear any signature data that might be stored
        if (questionToRemove.signatureData) {
          console.log('Clearing signature data for removed question')
        }
      }

      // Update the local state
      setQuestions(newQuestions)
      setOpenMenuQid(null)

      // Save the updated form if it's not a new form
      if (id && id !== 'new') {
        console.log('Saving updated form to backend')
        updateForm(parseInt(id, 10), {
          name,
          documentType: documentType || 'Form',
          questions: newQuestions,
          status: 'Draft',
          mandatory,
          notify,
          expiration
        }).then(() => {
          console.log('Form updated successfully after question removal')
        }).catch((error) => {
          console.error('Error updating form after question removal:', error)
        })
      }
    } catch (error) {
      console.error('Error in removeQuestion:', error)
    }
  }

  const duplicateQuestion = (qid) => {
    const idx = questions.findIndex(q => q.id === qid)
    if (idx === -1) return
    const newId = Math.max(...questions.map(q => q.id)) + 1
    const newQ = { ...questions[idx], id: newId }
    const newQuestions = [...questions.slice(0, idx + 1), newQ, ...questions.slice(idx + 1)]
    setQuestions(newQuestions)
    if (id && id !== 'new') {
      updateForm(parseInt(id, 10), { questions: newQuestions })
    }
  }

  const moveQuestion = (qid, direction) => {
    setQuestions(prevQs => {
      const idx = prevQs.findIndex(q => q.id === qid)
      if (idx < 0) return prevQs
      const newQuestions = [...prevQs]
      const [removed] = newQuestions.splice(idx, 1)
      newQuestions.splice(direction === 'up' ? idx - 1 : idx + 1, 0, removed)

      // Persist if editing existing form
      if (id && id !== 'new') {
        updateForm(parseInt(id, 10), {
          name,
          documentType: documentType || 'Form',
          questions: newQuestions,
          status: 'Draft',
          mandatory,
          notify,
          expiration,
        })
      }

      return newQuestions
    })
  }

  const handleSave = async (status) => {
    setSavingStatus(status)
    setIsSaving(true)
    try {
      setSaveError(null)
      
      // Make sure status is correctly formatted (API expects "Published" or "Draft")
      const formattedStatus = status === 'Draft' ? 'Draft' : 'Published';
      
      console.log('Saving form with questions:', questions);
      
      if (id && id !== 'new') {
        // Update existing form
        console.log(`Updating form ${id} with status ${formattedStatus}`);
        await updateForm(parseInt(id, 10), {
          name,
          documentType: documentType || 'Form',
          questions,
          status: formattedStatus,
          mandatory,
          notify,
          expiration,
        })
      } else {
        // Add new form
        const newId = forms.length ? Math.max(...forms.map(f => f.id)) + 1 : 1
        console.log(`Creating new form with ID ${newId} and status ${formattedStatus}`);
        await addForm({
          id: newId,
          name,
          documentType: documentType || 'Form',
          createdDate: new Date().toISOString().slice(0, 10),
          status: formattedStatus,
          createdBy,
          questions,
          mandatory,
          notify,
          expiration,
        })
      }
      
      /* ----------------------------------------------------------- */
      /* Generate & upload PDF when publishing                       */
      /* ----------------------------------------------------------- */
      if (formattedStatus === 'Published') {
        try {
          const sanitisedName = name.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'form'
          const pdfFilename = `${sanitisedName}_${Date.now()}.pdf`
          await saveFormToPdf(name, questions, pdfFilename, {
            documentType: documentType || 'Form',
            skipUpload: true, // prevent upload to S3 – customer app reads form from backend JSON
          })
          console.log('Form PDF generated and upload initiated')
        } catch (pdfErr) {
          console.error('Failed to generate/upload form PDF:', pdfErr)
          // We do not treat this as fatal for the form save itself
        }
      }

      // Success modal AFTER all async work (inc. PDF for Publish)
      setSaveSuccess({ visible: true, status: formattedStatus })

    } catch (error) {
      console.error('Error saving form:', error)
      setSaveError(`Failed to save form: ${error.message}`)
      setIsSaving(false)
    } finally {
      setIsSaving(false)
      setSavingStatus(null)
    }
  }

  // Question drag handlers
  const handleQuestionDragStart = (qid) => (e) => {
    e.stopPropagation()
    setDraggedQid(qid)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', String(qid))
  }

  const handleQuestionDragOver = (qid) => (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOverQid(qid)
    e.dataTransfer.dropEffect = 'move'
  }

  const handleQuestionDrop = (qid) => (e) => {
    e.preventDefault()
    e.stopPropagation()
    const reorderQid = Number(e.dataTransfer.getData('text/plain'))
    if (reorderQid && reorderQid !== qid) {
      const fromIdx = questions.findIndex(q => q.id === reorderQid)
      const toIdx = questions.findIndex(q => q.id === qid)
      if (fromIdx !== -1 && toIdx !== -1) {
        const newQuestions = [...questions]
        const [moved] = newQuestions.splice(fromIdx, 1)
        newQuestions.splice(toIdx, 0, moved)
        setQuestions(newQuestions)
        if (id && id !== 'new') {
          updateForm(parseInt(id, 10), { questions: newQuestions })
        }
      }
    }
    setDraggedQid(null)
    setDragOverQid(null)
  }

  const handleQuestionDragEnd = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDraggedQid(null)
    setDragOverQid(null)
  }

  /* ----------------------------------------------------------- */
  /* Local PDF generation (manual user action)                   */
  /* ----------------------------------------------------------- */
  const handleDownloadPdf = async () => {
    try {
      setIsGeneratingPdf(true)
      const sanitisedName = name.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'form'
      const pdfFilename = `${sanitisedName}_${Date.now()}.pdf`
      await saveFormToPdf(name, questions, pdfFilename, {
        documentType: documentType || 'Form',
        download: true,      // force local save
        skipUpload: true,    // do not upload when user uses manual button
      })
    } catch (err) {
      console.error('Failed to generate local PDF:', err)
    } finally {
      setIsGeneratingPdf(false)
    }
  }

  return (
    <>
      <SaveSuccessModal
        open={saveSuccess.visible}
        status={saveSuccess.status}
        onClose={() => {
          setSaveSuccess({ visible: false, status: '' })
          navigate('/forms')
        }}
      />
      <div className="flex max-w-5xl mx-auto py-10">
        {/* Sidebar */}
        <div className="w-56 min-w-[14rem] max-w-[14rem] pr-6 border-r flex-shrink-0 sticky top-20 h-[calc(100vh-5rem)] overflow-y-auto">
          <div className="font-semibold mb-4">Add Question</div>
          <div className="grid grid-cols-2 gap-3">
            {questionTypes.map(qt => (
              <DraggableQuestionType key={qt.type} qt={qt} />
            ))}
          </div>
        </div>
        {/* Main Canvas */}
        <div className="flex-1 px-8 min-w-0" onDragOver={handleDragOver} onDrop={handleDrop}>
          <div className="mb-8 flex items-center space-x-3">
            <AutoResizeTextarea
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Form title"
              className="text-2xl font-bold border-b border-gray-300 focus:border-blue-500 outline-none flex-1 rounded-none border-x-0 border-t-0 px-0 py-1"
            />
            {/* View-mode toggle buttons (Design / Desktop Preview / Mobile Preview) */}
            <div className="flex items-center gap-2 ml-4">
              <button
                type="button"
                onClick={() => setViewMode('design')}
                className={`px-3 py-2 rounded text-sm font-medium transition-colors ${viewMode === 'design' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-blue-100'}`}
              >
                Design
              </button>
              <button
                type="button"
                onClick={() => setViewMode('preview-desktop')}
                className={`px-3 py-2 rounded text-sm font-medium transition-colors ${viewMode === 'preview-desktop' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-blue-100'}`}
              >
                🖥️ Desktop Preview
              </button>
              <button
                type="button"
                onClick={() => setViewMode('preview-mobile')}
                className={`px-3 py-2 rounded text-sm font-medium transition-colors ${viewMode === 'preview-mobile' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-blue-100'}`}
              >
                📱 Mobile Preview
              </button>
            </div>
          </div>
          {viewMode === 'design' ? (
            <div className="space-y-6">
              {questions.map((q, idx) => (
                <div
                  key={q.id}
                  className={`bg-white border rounded p-0 shadow-sm relative ${dragOverQid === q.id && draggedQid !== null ? 'ring-2 ring-blue-400' : ''}`}
                  onDragOver={handleQuestionDragOver(q.id)}
                  onDrop={handleQuestionDrop(q.id)}
                  onDragEnd={handleQuestionDragEnd}
                >
                  <div className="p-4">
                    <div className="flex items-center mb-2">
                      <span className="font-semibold mr-2">Q{idx + 1}.</span>
                      <span className="px-2 py-1 bg-gray-100 rounded text-sm mr-2">{questionTypes.find(qt => qt.type === q.type)?.label || q.type}</span>
                      {/* Only show label input if not text type */}
                      {q.type !== 'text' && q.type !== 'signature' && (
                        <input
                          className="flex-1 border-b border-gray-200 focus:border-blue-400 outline-none text-sm"
                          placeholder="Ask your question"
                          value={q.label}
                          onChange={e => updateQuestion(q.id, 'label', e.target.value)}
                        />
                      )}
                    </div>
                    {/* Render for text type only: label, dropdown, and text input */}
                    {q.type === 'text' && (
                      <div className="space-y-2 mt-2">
                        <select
                          className="w-full border rounded px-2 py-2 text-sm"
                          value={q.textStyle || 'Heading'}
                          onChange={e => updateQuestion(q.id, 'textStyle', e.target.value)}
                        >
                          <option value="Heading">Heading</option>
                          <option value="Paragraph">Paragraph</option>
                          <option value="Subheading">Subheading</option>
                        </select>
                        {/* Auto-resizing textarea for text content */}
                        <AutoResizeTextarea
                          value={q.textContent || ''}
                          placeholder="Enter your text here"
                          onChange={e => updateQuestion(q.id, 'textContent', e.target.value)}
                        />
                      </div>
                    )}
                    {q.type === 'short' && (
                      <input
                        className="w-full border rounded px-2 py-2 text-sm mt-2"
                        placeholder="Answer"
                        disabled
                      />
                    )}
                    {q.type === 'long' && (
                      <textarea
                        className="w-full border rounded px-2 py-2 text-sm mt-2"
                        placeholder="Answer"
                        rows={3}
                        disabled
                      />
                    )}
                    {q.type === 'multiple' && (
                      <div className="space-y-2 mt-2">
                        {q.options.map((opt, i) => (
                          <input
                            key={i}
                            className="w-full border rounded px-2 py-2 text-sm"
                            value={opt}
                            placeholder={`Option ${i + 1}`}
                            onChange={e => updateOption(q.id, i, e.target.value)}
                          />
                        ))}
                        <button className="text-blue-500 hover:underline text-xs mt-1" onClick={() => addOption(q.id)} type="button">+ Add an option</button>
                      </div>
                    )}
                    {/* Signature type */}
                    {q.type === 'signature' && (
                      <div className="mt-2">
                        <Signature 
                          initialData={q.signatureData || {}}
                          onSave={(signatureData) => {
                            updateQuestion(q.id, 'signatureData', signatureData);
                          }} 
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex items-center justify-between border-t px-4 py-2 bg-gray-50 rounded-b">
                    {/* Drag handle */}
                    <div
                      className="cursor-move text-2xl text-gray-400 hover:text-blue-500 select-none"
                      title="Drag to reorder"
                      draggable
                      onDragStart={handleQuestionDragStart(q.id)}
                      style={{ width: '32px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    >
                      <span style={{ fontFamily: 'monospace', fontWeight: 'bold', fontSize: '20px', lineHeight: '16px' }}>≡</span>
                    </div>
                    <div className="flex items-center gap-4">
                      {/* Required toggle — hidden for static text questions */}
                      {q.type !== 'text' && (
                        <label className="flex items-center text-xs font-medium cursor-pointer">
                          <input
                            type="checkbox"
                            checked={q.required}
                            onChange={e => updateQuestion(q.id, 'required', e.target.checked)}
                            className="mr-1"
                          />
                          Required
                        </label>
                      )}
                      {/* More menu */}
                      <div className="relative" ref={openMenuQid === q.id ? menuRef : null}>
                        <button
                          className="text-gray-400 hover:text-gray-700 text-lg px-2"
                          type="button"
                          onClick={() => setOpenMenuQid(openMenuQid === q.id ? null : q.id)}
                        >
                          ⋮
                        </button>
                        {openMenuQid === q.id && (
                          <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
                            <button
                              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              type="button"
                              onClick={() => { duplicateQuestion(q.id); setOpenMenuQid(null); }}
                            >
                              Duplicate
                            </button>
                            <button
                              className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                              type="button"
                              onClick={() => { removeQuestion(q.id); setOpenMenuQid(null); }}
                            >
                              Delete
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {questions.length === 0 && (
                <div className="text-gray-400 text-center py-8 border-2 border-dashed border-gray-300 rounded-lg" onDragOver={handleDragOver} onDrop={handleDrop}>
                  Drag and drop a question type here to add it to your form
                </div>
              )}
            </div>
          ) : (
            <FormPreviewFrame key={viewMode} initialMode={viewMode === 'preview-mobile' ? 'mobile' : 'desktop'}>
              <div>
                <h2 className="text-3xl font-bold mb-2">{name}</h2>
                <hr className="border-b border-gray-300 mb-6" />
                <div className="space-y-6">
                  {questions.map((q, idx) => {
                    const isStaticText = q.type === 'text';
                    const isSignature = q.type === 'signature';
                    return (
                      <div
                        key={q.id}
                        className={(isStaticText || isSignature) ? 'mb-6' : 'bg-white border rounded p-4 shadow-sm mb-6'}
                      >
                        {q.type !== 'text' && q.type !== 'signature' && (
                          <div className="mb-2">
                            <span className="text-sm font-medium">{q.label}</span>
                          </div>
                        )}
                        
                        {/* Static text rendering */}
                        {q.type === 'text' && (
                          q.textStyle === 'Heading' ? (
                            <h2 className="text-3xl font-bold mb-2">{q.textContent}</h2>
                          ) : q.textStyle === 'Subheading' ? (
                            <h3 className="text-xl font-semibold mb-1">{q.textContent}</h3>
                          ) : (
                            <p className="text-base leading-relaxed">{q.textContent}</p>
                          )
                        )}
                        
                        {/* Short answer */}
                        {q.type === 'short' && (
                          <input className="w-full border rounded px-2 py-2 text-sm" disabled />
                        )}
                        
                        {q.type === 'long' && (
                          <textarea className="w-full border rounded px-2 py-2 text-sm" rows={3} disabled />
                        )}
                        
                        {q.type === 'multiple' && (
                          <div className="space-y-2">
                            {q.options?.map((opt, i) => (
                              <div key={i} className="flex items-center">
                                <input type="radio" name={`q${q.id}`} id={`q${q.id}_opt${i}`} disabled />
                                <label htmlFor={`q${q.id}_opt${i}`} className="ml-2 text-sm">{opt}</label>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        {/* Signature field */}
                        {q.type === 'signature' && (
                          <div className="space-y-4">
                            <div className="text-red-600 font-medium">Please sign here *</div>
                            <div className="flex items-center mb-3">
                              <input type="checkbox" checked={q.signatureData?.agreed} disabled className="mr-2" />
                              <label className="text-gray-500 text-sm">I agree to use electronic records and signatures.</label>
                            </div>
                            {q.signatureData?.customer ? (
                              <div className="border-b-2 border-gray-600 pb-4">
                                <img src={q.signatureData.customer} alt="Customer Signature" className="max-h-32" />
                              </div>
                            ) : (
                              <div className="h-24 border-b-2 border-gray-600 mb-2"></div>
                            )}
                          </div>
                        )}
                        
                        {q.required && !isStaticText && !isSignature && (
                          <div className="mt-2 text-xs text-red-500">* Required</div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </FormPreviewFrame>
          )}
          {/* settings bar at the bottom */}
          <div
            className="
              mt-10 border-t bg-white rounded shadow-sm
              /* pretty padding */
              px-8 py-6
              /* two‑column grid: info | buttons  */
              grid gap-8 md:grid-cols-[1fr_auto] items-center
            "
          >
            {/* left‑hand toggles / input */}
            <div className="flex flex-wrap items-center gap-8">
              <label className="flex items-center text-sm font-medium">
                <input
                  type="checkbox"
                  checked={mandatory}
                  onChange={e => setMandatory(e.target.checked)}
                  className="mr-2"
                />
                Mandatory for all clients
              </label>

              <label className="flex items-center text-sm font-medium">
                <input
                  type="checkbox"
                  checked={notify}
                  onChange={e => setNotify(e.target.checked)}
                  className="mr-2"
                />
                Notify on submit
              </label>

              <label className="flex items-center text-sm font-medium">
                Expiration period&nbsp;(months):
                <input
                  type="number"
                  min="0"
                  className="ml-2 border rounded px-2 py-1 w-24"
                  value={expiration}
                  onChange={e => setExpiration(e.target.value)}
                />
              </label>
            </div>

            {/* right‑hand buttons */}
            <div className="flex flex-wrap gap-4 justify-end min-w-[340px]">
              <button
                className="inline-flex items-center justify-center px-6 py-2
                          border border-gray-300 rounded text-gray-700
                          hover:bg-gray-50 whitespace-nowrap"
                onClick={() => navigate('/forms')}
                type="button"
              >
                Cancel
              </button>

              <button
                className="inline-flex items-center justify-center px-6 py-2
                          border border-gray-300 rounded text-gray-700
                          hover:bg-gray-50 whitespace-nowrap"
                onClick={() => handleSave('Draft')}
                type="button"
                disabled={isSaving && savingStatus === 'Draft'}
              >
                {(isSaving && savingStatus === 'Draft') ? 'Saving…' : 'Save for Later'}
              </button>

              <button
                className="inline-flex items-center justify-center px-6 py-2
                          bg-blue-600 text-white rounded hover:bg-blue-700
                          font-medium whitespace-nowrap"
                onClick={handleDownloadPdf}
                type="button"
                disabled={isGeneratingPdf}
              >
                {isGeneratingPdf ? 'Generating…' : 'Save to PDF'}
              </button>

              <button
                className="inline-flex items-center justify-center px-6 py-2
                          bg-green-600 text-white rounded hover:bg-green-700
                          font-medium whitespace-nowrap"
                onClick={() => handleSave('Published')}
                type="button"
                disabled={isSaving && savingStatus === 'Published'}
              >
                {(isSaving && savingStatus === 'Published') ? 'Publishing…' : 'Publish'}
              </button>
            </div>
          </div>

        </div>
      </div>
    </>
  )
}

export default FormsEdit
