# Generated by Django 4.2.22 on 2025-08-07 12:43

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import utils.timezone_validators
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('business', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='employees/profile/')),
                ('calendar_sync_enabled', models.BooleanField(default=False, verbose_name='Enable Calendar Sync')),
                ('calendar_provider', models.CharField(blank=True, choices=[('google', 'Google Calendar'), ('outlook', 'Outlook Calendar'), ('apple', 'Apple Calendar')], max_length=50, null=True)),
                ('calendar_id', models.Char<PERSON>ield(blank=True, help_text='Calendar ID from the third-party calendar service', max_length=255, null=True)),
                ('ics_feed_token', models.UUIDField(default=uuid.uuid4, help_text='Unique token for ICS calendar feed subscription', unique=True)),
                ('accept_online_bookings', models.BooleanField(default=True, verbose_name='Accept Online Bookings')),
                ('employee_type', models.CharField(choices=[('service_provider', 'Service Provider'), ('admin', 'Account Admin')], default='service_provider', max_length=20, verbose_name='Employee Type')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('access_level', models.ForeignKey(blank=True, limit_choices_to=models.Q(('business', models.F('business'))), null=True, on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='business.accesslevel')),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='business.business')),
                ('stylist_level', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='business.stylistlevel', verbose_name='Stylist Level')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Employee Profile',
                'verbose_name_plural': 'Employee Profiles',
                'ordering': ['user__last_name', 'user__first_name', 'pk'],
            },
        ),
        migrations.CreateModel(
            name='AccessLevel',
            fields=[
            ],
            options={
                'verbose_name': 'Access Level',
                'verbose_name_plural': 'Access Levels',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('business.accesslevel',),
        ),
        migrations.CreateModel(
            name='EmployeeWorkingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], default='monday', help_text='Day of the week', max_length=10)),
                ('start_time', models.TimeField(default=datetime.time(9, 0), help_text='Start time of the working block')),
                ('end_time', models.TimeField(default=datetime.time(17, 0), help_text='End time of the working block')),
                ('timezone', models.CharField(blank=True, help_text='Timezone for these working hours. If blank, uses business timezone.', max_length=50, null=True, validators=[utils.timezone_validators.validate_working_hours_timezone])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_hours', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Employee working hours',
                'verbose_name_plural': 'Employee working hours',
                'ordering': ['employee', 'day'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeCalendarConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('week_start_day', models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], default='monday', help_text='Week View Starting Day', max_length=10)),
                ('default_view', models.CharField(choices=[('day', 'Day'), ('week', 'Week')], default='week', help_text='Default View at Login', max_length=10)),
                ('calendar_resolution', models.PositiveSmallIntegerField(default=15, help_text='Calendar time resolution in minutes')),
                ('display_hour_start', models.PositiveSmallIntegerField(default=7, help_text='Display start hour for calendar view (24h format, default 7 AM)')),
                ('display_hour_end', models.PositiveSmallIntegerField(default=23, help_text='Display end hour for calendar view (24h format, default 11 PM)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_config', to='employees.employee')),
            ],
        ),
        migrations.AddConstraint(
            model_name='employeeworkinghours',
            constraint=models.UniqueConstraint(fields=('employee', 'day'), name='unique_employee_day'),
        ),
        migrations.AddConstraint(
            model_name='employeeworkinghours',
            constraint=models.CheckConstraint(check=models.Q(('start_time__lt', models.F('end_time'))), name='working_hours_start_before_end'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['is_active'], name='employee_active_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['stylist_level'], name='employee_level_idx'),
        ),
        migrations.AddConstraint(
            model_name='employee',
            constraint=models.UniqueConstraint(fields=('business', 'user'), name='unique_employee_per_business'),
        ),
    ]
