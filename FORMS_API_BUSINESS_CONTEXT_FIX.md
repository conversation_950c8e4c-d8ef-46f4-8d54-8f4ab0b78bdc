# Forms API Business Context Fix

## Issue Description

When a customer created a mandatory form in the business frontend, the customer frontend would encounter errors because the forms API (`GET /api/v1/business-customers/me/forms/`) was not correctly fetching forms for the specific business the customer was booking with.

### Root Cause

The `CustomerFormCompletionView` API endpoint was using `.first()` to get any business customer relationship, rather than checking the specific business the customer was booking with. This meant:

1. If a customer had relationships with multiple businesses
2. And the first business had no required forms
3. The API would return `all_forms_completed: true`
4. Even if the customer was booking with a different business that had required forms

### Problem Scenario

```
Customer has relationships with:
- Business A (no required forms) - first in database
- Business B (has required forms) - customer is booking here

API would check Business A and return all_forms_completed: true
Customer would skip consent form and go directly to review
```

## Solution

### Backend Changes

**File: `chatbook-backend/api/v1/business/views.py`**

1. **Modified `CustomerFormCompletionView.get()` method** to accept a `business_id` query parameter
2. **Added business-specific form checking** - when `business_id` is provided, the endpoint checks forms for that specific business
3. **Added automatic BusinessCustomer relationship creation** - if no relationship exists for the specified business, one is created
4. **Maintained backward compatibility** - if no `business_id` is provided, falls back to the original behavior

Key changes:
```python
# Get business ID from query parameters
business_id = request.query_params.get('business_id')

if business_id:
    # Get the business customer relationship for the specific business
    try:
        business = get_object_or_404(Business, id=business_id)
        business_customer = BusinessCustomer.objects.get(
            customer=customer_profile,
            business=business
        )
    except BusinessCustomer.DoesNotExist:
        # If no relationship exists, create one for new customers
        business_customer = BusinessCustomer.objects.create(
            customer=customer_profile,
            business=business
        )
else:
    # Fallback to original behavior - get any business customer relationship
    business_customer = BusinessCustomer.objects.filter(customer=customer_profile).first()
```

### Frontend Changes

**File: `chatbook-customer-frontend/src/features/auth/services/authApi.js`**

1. **Updated `refreshConsentStatus()` function** to accept an optional `businessId` parameter
2. **Modified login flow** to use default business ID of 1 during initial login
3. **Updated API URL construction** for consent status checks

**File: `chatbook-customer-frontend/src/api/consentApi.js`**

1. **Updated `checkUserConsentStatus()` function** to accept an optional `businessId` parameter
2. **Modified API URL construction** to include `business_id` query parameter when provided

**File: `chatbook-customer-frontend/src/stores/authStore.js`**

1. **Added business-specific consent status storage** - `consentStatusByBusiness` field
2. **Updated consent status methods** to work with business-specific data
3. **Maintained backward compatibility** with legacy `consentStatus` field

**File: `chatbook-customer-frontend/src/pages/ConsentFormPage.jsx`**

1. **Added business ID extraction** from booking data: `bookingData?.selectedService?.business || bookingData?.businessId || 1`
2. **Updated consent status API calls** to pass the business ID
3. **Updated form submission refresh** to use the correct business context

**File: `chatbook-customer-frontend/src/utils/navigationUtils.js`**

1. **Updated navigation functions** to accept booking data parameter
2. **Added business ID extraction** from booking data
3. **Updated consent status checks** to use business-specific data

**File: `chatbook-customer-frontend/src/components/booking/BookingPage.jsx`**

1. **Updated `handlePostTimeSlotSelection` call** to pass booking data

## Business ID Determination

The frontend determines the business ID using this priority order:
1. `bookingData?.selectedService?.business` - from the selected service
2. `bookingData?.businessId` - from booking session data
3. Default to `1` - fallback for login and edge cases

## API Usage Examples

### Without business_id (legacy behavior)
```bash
GET /api/v1/business-customers/me/forms/
```

### With business_id (new behavior)
```bash
GET /api/v1/business-customers/me/forms/?business_id=1
```

### Frontend API calls
```javascript
// During login (default business)
await authService.refreshConsentStatus(1);

// During booking (specific business)
const businessId = bookingData?.selectedService?.business || 1;
await authService.refreshConsentStatus(businessId);
await checkUserConsentStatus(businessId);
```

## Benefits

1. **Correct Business Context**: Forms are now checked for the specific business the customer is booking with
2. **Automatic Relationship Creation**: New customers automatically get business relationships created
3. **Backward Compatibility**: Existing code continues to work without changes
4. **Multi-Business Support**: Customers can have different form completion statuses for different businesses
5. **Proper Form Flow**: Customers are correctly directed to consent forms when needed

## Testing

Use the provided test script to verify the API works correctly:

```bash
python test_forms_api.py
```

This will test:
1. API without business_id parameter
2. API with business_id=1
3. API with business_id=2 (should create new relationship)

## Migration Notes

- **No database migrations required** - existing data remains unchanged
- **Backward compatible** - existing API calls continue to work
- **Progressive enhancement** - new business_id parameter is optional
- **Automatic fallback** - if business_id is not provided, uses original behavior

## Future Enhancements

1. **Business ID in URL**: Consider adding business ID to customer frontend URLs
2. **Business Selection**: Add UI for customers to select which business they're booking with
3. **Multi-Business Dashboard**: Show form completion status across all businesses
4. **Business-Specific Settings**: Allow different form requirements per business
