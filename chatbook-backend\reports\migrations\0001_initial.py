# Generated by Django 4.2.22 on 2025-08-07 12:43

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('appointments', '0002_initial'),
        ('business', '0002_initial'),
        ('services', '0001_initial'),
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.CharField(help_text='Transaction ID from external system', max_length=50, primary_key=True, serialize=False, verbose_name='Transaction ID')),
                ('checkout_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Date and time when the transaction was processed')),
                ('item_sold', models.CharField(help_text='Description of item/service sold', max_length=255)),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Quantity of service/item', validators=[django.core.validators.MinValueValidator(1)])),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('credit_card', 'Credit Card'), ('debit_card', 'Debit Card'), ('check', 'Check'), ('paypal', 'Paypal'), ('stripe', 'Stripe'), ('venmo', 'Venmo'), ('zelle', 'Zelle'), ('other', 'Other')], help_text='Method of payment used for the transaction', max_length=20)),
                ('price', models.DecimalField(decimal_places=2, help_text='Base price of the service/item', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('tax', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Tax amount applied', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('tip', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Tip amount given', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('discount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Discount amount applied', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('amount_paid', models.DecimalField(decimal_places=2, help_text='Total amount paid by customer', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.ForeignKey(blank=True, help_text='Appointment this transaction is paying for (when transaction comes from completed appointment)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='appointments.appointment')),
                ('appointment_add_on', models.ForeignKey(blank=True, help_text='Specific add-on within appointment being paid for (allows proper sales categorization)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='appointments.appointmentaddon')),
                ('appointment_service', models.ForeignKey(blank=True, help_text='Specific service within appointment being paid for (allows multiple transactions per appointment)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='appointments.appointmentservice')),
                ('checkout_by', models.ForeignKey(help_text='Employee who processed the checkout', on_delete=django.db.models.deletion.PROTECT, related_name='transactions_processed', to='employees.employee')),
                ('customer', models.ForeignKey(help_text='Customer who received the service', on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='business.businesscustomer')),
                ('service', models.ForeignKey(help_text='Service that was provided', on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='services.service')),
                ('sold_by', models.ForeignKey(help_text='Employee who performed/sold the service', on_delete=django.db.models.deletion.PROTECT, related_name='transactions_sold', to='employees.employee')),
            ],
            options={
                'ordering': ['-checkout_date', '-id'],
                'indexes': [models.Index(fields=['checkout_date'], name='reports_tra_checkou_20dc47_idx'), models.Index(fields=['sold_by', 'checkout_date'], name='reports_tra_sold_by_1b64f3_idx'), models.Index(fields=['customer', 'checkout_date'], name='reports_tra_custome_db0eb6_idx'), models.Index(fields=['service'], name='reports_tra_service_d398c6_idx'), models.Index(fields=['appointment', 'checkout_date'], name='reports_tra_appoint_b5080c_idx'), models.Index(fields=['appointment_service'], name='reports_tra_appoint_c7d727_idx'), models.Index(fields=['appointment_add_on'], name='reports_tra_appoint_4e138a_idx')],
            },
        ),
    ]
