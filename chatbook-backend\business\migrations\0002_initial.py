# Generated by Django 4.2.22 on 2025-08-07 12:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        ('business', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='businesscustomer',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_relationships', to='customers.customerprofile'),
        ),
        migrations.AddField(
            model_name='businesscustomer',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='business_customers', to='customers.customertag'),
        ),
        migrations.AddField(
            model_name='business',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_businesses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accesslevel',
            name='business',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_levels', to='business.business'),
        ),
        migrations.AddConstraint(
            model_name='stylistlevel',
            constraint=models.UniqueConstraint(fields=('business', 'name'), name='unique_stylist_level_name_per_business'),
        ),
        migrations.AlterUniqueTogether(
            name='smartbookingrule',
            unique_together={('business', 'rule_type')},
        ),
        migrations.AlterUniqueTogether(
            name='businessuser',
            unique_together={('business', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='businesscustomer',
            unique_together={('business', 'customer')},
        ),
        migrations.AlterUniqueTogether(
            name='accesslevel',
            unique_together={('business', 'level')},
        ),
    ]
